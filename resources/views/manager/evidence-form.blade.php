@extends('layouts.app')

@section('styles')
<style>
/* Ultra Compact Evidence Form - Single View */
.evidence-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    padding: 0.75rem;
    margin-bottom: 1rem;
    color: white;
    box-shadow: 0 2px 10px rgba(102, 126, 234, 0.15);
}

.evidence-card {
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.06);
    margin-bottom: 0.75rem;
}

.file-input-compact {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 0.5rem;
    border: 1px dashed #dee2e6;
    margin-bottom: 0.5rem;
}

.evidence-item-compact {
    background: rgba(102, 126, 234, 0.1);
    border-left: 3px solid #667eea;
    padding: 0.5rem;
    border-radius: 0 6px 6px 0;
    margin-bottom: 0.25rem;
}

.btn-compact {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: 4px;
}

.form-control-xs {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: 4px;
    height: auto;
}

.card-header-compact {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 8px 8px 0 0;
}

.upload-zone-compact {
    background: #f8f9fa;
    border: 1px dashed #dee2e6;
    border-radius: 6px;
    padding: 1rem;
    text-align: center;
    margin-bottom: 0.75rem;
}

.upload-icon-compact {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 0.5rem;
    color: white;
    font-size: 1rem;
}

.status-badge-compact {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    padding: 0.125rem 0.5rem;
    font-size: 0.65rem;
}

.glass-effect {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Ultra compact layout */
.row-compact { margin: 0 -0.25rem; }
.col-compact { padding: 0 0.25rem; }
.mb-compact { margin-bottom: 0.5rem !important; }
.p-compact { padding: 0.5rem !important; }
.py-compact { padding-top: 0.25rem !important; padding-bottom: 0.25rem !important; }

/* Hide scrollbars but keep functionality */
.evidence-scroll {
    max-height: 200px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(102, 126, 234, 0.3) transparent;
}

.evidence-scroll::-webkit-scrollbar {
    width: 4px;
}

.evidence-scroll::-webkit-scrollbar-track {
    background: transparent;
}

.evidence-scroll::-webkit-scrollbar-thumb {
    background: rgba(102, 126, 234, 0.3);
    border-radius: 2px;
}

/* Station Information Section */
.station-info-item {
    transition: all 0.3s ease;
    border-radius: 6px;
    padding: 0.25rem;
}

.station-info-item:hover {
    background: rgba(102, 126, 234, 0.05);
    transform: translateY(-1px);
}

.station-avatar {
    transition: all 0.3s ease;
}

.station-avatar:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}
</style>
@endsection

@section('content')
<div class="container-fluid">
    <!-- Ultra Compact Header -->
    <div class="evidence-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h6 class="mb-0 fw-bold">
                    <i class="bi bi-cloud-upload me-1"></i>
                    Upload Evidence
                </h6>
            </div>
            <a href="{{ route('manager.dashboard') }}" class="btn btn-compact glass-effect text-white">
                <i class="bi bi-arrow-left"></i>
            </a>
        </div>
    </div>

    <!-- Compact Station Information Section -->
    <div class="row row-compact mb-compact">
        <div class="col-12">
            <div class="evidence-card">
                <div class="card-body p-compact">
                    <div class="row row-compact g-1">
                        <div class="col-md-4 col-compact">
                            <div class="station-info-item d-flex align-items-center">
                                <div class="me-2">
                                    <i class="bi bi-building text-primary" style="font-size: 1.2rem;"></i>
                                </div>
                                <div>
                                    <div class="fw-bold" style="font-size: 0.8rem;">{{ $station->name }}</div>
                                    <small class="text-muted" style="font-size: 0.7rem;">ID: {{ $station->id }}</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 col-compact">
                            <div class="station-info-item d-flex align-items-center">
                                <div class="me-2">
                                    <i class="bi bi-geo-alt text-info" style="font-size: 1.2rem;"></i>
                                </div>
                                <div>
                                    <div class="fw-bold" style="font-size: 0.8rem;">{{ $station->district }}</div>
                                    <small class="text-muted" style="font-size: 0.7rem;">{{ $station->county }}, {{ $station->subcounty }}</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 col-compact">
                            @if($agent && $agent->user)
                            <div class="station-info-item d-flex align-items-center">
                                <div class="me-2">
                                    <div class="station-avatar rounded-circle d-flex align-items-center justify-content-center"
                                         style="width: 24px; height: 24px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; font-size: 0.7rem; font-weight: bold;">
                                        {{ substr($agent->user->name, 0, 1) }}
                                    </div>
                                </div>
                                <div>
                                    <div class="fw-bold" style="font-size: 0.8rem;">{{ $agent->user->name }}</div>
                                    <small class="text-muted" style="font-size: 0.7rem;">{{ $agent->user->phone_number }}</small>
                                </div>
                            </div>
                            @else
                            <div class="station-info-item d-flex align-items-center">
                                <div class="me-2">
                                    <i class="bi bi-person-x text-warning" style="font-size: 1.2rem;"></i>
                                </div>
                                <div>
                                    <div class="fw-bold text-warning" style="font-size: 0.8rem;">No Agent</div>
                                    <small class="text-muted" style="font-size: 0.7rem;">Not assigned</small>
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Station Info Card -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-primary">Station Information</h6>
                    <table class="table table-sm table-borderless">
                        <tr><td><strong>Name:</strong></td><td>{{ $station->name }}</td></tr>
                        <tr><td><strong>District:</strong></td><td>{{ $station->district }}</td></tr>
                        <tr><td><strong>County:</strong></td><td>{{ $station->county }}</td></tr>
                        <tr><td><strong>Subcounty:</strong></td><td>{{ $station->subcounty }}</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6 class="text-primary">Agent Information</h6>
                    @if($agent && $agent->user)
                        <table class="table table-sm table-borderless">
                            <tr><td><strong>Name:</strong></td><td>{{ $agent->user->name }}</td></tr>
                            <tr><td><strong>Phone:</strong></td><td>{{ $agent->user->phone_number }}</td></tr>
                        </table>
                    @else
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            No agent assigned to this station
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    @if(!$agent)
        <div class="alert alert-danger py-compact mb-compact">
            <small><i class="bi bi-x-circle me-1"></i><strong>Cannot Upload:</strong> No agent assigned to this station.</small>
        </div>
    @else
        <!-- Single Row Layout -->
        <div class="row row-compact">
            <!-- Left: Existing Evidence -->
            <div class="col-md-6 col-compact">
                @if($existingEvidence && $existingEvidence->count() > 0)
                <div class="evidence-card">
                    <div class="card-header-compact">
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="fw-bold">
                                <i class="bi bi-files me-1"></i>
                                Existing Evidence
                            </small>
                            <span class="status-badge-compact">
                                {{ $existingEvidence->count() }}
                            </span>
                        </div>
                    </div>
                    <div class="card-body p-compact evidence-scroll">
                        @foreach($existingEvidence as $evidence)
                        <div class="evidence-item-compact">
                            <div class="d-flex align-items-center">
                                <div class="me-2">
                                    @if(in_array(pathinfo($evidence->file_url, PATHINFO_EXTENSION), ['jpg', 'jpeg', 'png']))
                                        <i class="bi bi-image text-primary"></i>
                                    @elseif(pathinfo($evidence->file_url, PATHINFO_EXTENSION) === 'pdf')
                                        <i class="bi bi-file-earmark-pdf text-danger"></i>
                                    @else
                                        <i class="bi bi-file-earmark text-secondary"></i>
                                    @endif
                                </div>
                                <div class="flex-grow-1">
                                    <div class="fw-semibold" style="font-size: 0.75rem;">{{ $evidence->file_name ?: 'Evidence File' }}</div>
                                    <small class="text-muted" style="font-size: 0.65rem;">{{ $evidence->created_at->format('M d, H:i') }}</small>
                                </div>
                                <div>
                                    <a href="{{ asset('files/' . $evidence->file_url) }}"
                                       target="_blank"
                                       class="btn btn-compact status-badge-compact text-decoration-none">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
                @else
                <div class="evidence-card">
                    <div class="card-body p-compact text-center text-muted">
                        <i class="bi bi-files" style="font-size: 2rem; opacity: 0.5;"></i>
                        <div><small>No evidence files yet</small></div>
                    </div>
                </div>
                @endif
            </div>

            <!-- Right: Upload Form -->
            <div class="col-md-6 col-compact">

                <div class="evidence-card">
                    <div class="card-header-compact">
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="fw-bold">
                                <i class="bi bi-cloud-upload me-1"></i>
                                Upload New Evidence
                            </small>
                            <span class="status-badge-compact">
                                <i class="bi bi-shield-check"></i>
                            </span>
                        </div>
                    </div>
                    <div class="card-body p-compact">
                        <!-- Compact Upload Zone -->
                        <div class="upload-zone-compact mb-compact">
                            <div class="upload-icon-compact">
                                <i class="bi bi-cloud-upload"></i>
                            </div>
                            <small class="fw-bold">Drag & Drop or Click</small>
                            <div><small class="text-muted">JPG, PNG, PDF (Max 5MB)</small></div>
                        </div>

                        <form action="{{ route('manager.upload-evidence', $station) }}" method="POST" enctype="multipart/form-data" id="evidenceForm">
                            @csrf

                            <div id="fileInputs">
                                <div class="file-input-compact">
                                    <div class="row row-compact g-1">
                                        <div class="col-7 col-compact">
                                            <label class="form-label" style="font-size: 0.7rem; font-weight: 600;">
                                                <i class="bi bi-file-earmark me-1"></i>File
                                            </label>
                                            <input type="file"
                                                   class="form-control form-control-xs"
                                                   name="evidence_files[]"
                                                   accept=".jpg,.jpeg,.png,.pdf"
                                                   required>
                                        </div>
                                        <div class="col-4 col-compact">
                                            <label class="form-label" style="font-size: 0.7rem; font-weight: 600;">
                                                <i class="bi bi-tag me-1"></i>Description
                                            </label>
                                            <input type="text"
                                                   class="form-control form-control-xs"
                                                   name="file_names[]"
                                                   placeholder="DR Form">
                                        </div>
                                        <div class="col-1 col-compact d-flex align-items-end">
                                            <button type="button"
                                                    class="btn btn-compact btn-outline-danger remove-file"
                                                    onclick="removeFileInput(this)"
                                                    style="display: none; padding: 0.125rem 0.25rem;">
                                                <i class="bi bi-trash" style="font-size: 0.7rem;"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-compact">
                                <button type="button" class="btn btn-compact btn-outline-primary" onclick="addFileInput()" style="font-size: 0.7rem;">
                                    <i class="bi bi-plus-circle me-1"></i>
                                    Add File
                                </button>
                            </div>

                            <div class="text-center">
                                <button type="submit" class="btn btn-compact status-badge-compact w-100">
                                    <i class="bi bi-cloud-upload me-1"></i>
                                    Upload Evidence
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>

<script>
function addFileInput() {
    const container = document.getElementById('fileInputs');
    const newGroup = document.createElement('div');
    newGroup.className = 'file-input-compact';
    newGroup.innerHTML = `
        <div class="row row-compact g-1">
            <div class="col-7 col-compact">
                <label class="form-label" style="font-size: 0.7rem; font-weight: 600;">
                    <i class="bi bi-file-earmark me-1"></i>File
                </label>
                <input type="file"
                       class="form-control form-control-xs"
                       name="evidence_files[]"
                       accept=".jpg,.jpeg,.png,.pdf"
                       required>
            </div>
            <div class="col-4 col-compact">
                <label class="form-label" style="font-size: 0.7rem; font-weight: 600;">
                    <i class="bi bi-tag me-1"></i>Description
                </label>
                <input type="text"
                       class="form-control form-control-xs"
                       name="file_names[]"
                       placeholder="DR Form">
            </div>
            <div class="col-1 col-compact d-flex align-items-end">
                <button type="button"
                        class="btn btn-compact btn-outline-danger remove-file"
                        onclick="removeFileInput(this)"
                        style="padding: 0.125rem 0.25rem;">
                    <i class="bi bi-trash" style="font-size: 0.7rem;"></i>
                </button>
            </div>
        </div>
    `;

    container.appendChild(newGroup);
    updateRemoveButtons();
}

function removeFileInput(button) {
    button.closest('.file-input-compact').remove();
    updateRemoveButtons();
}

function updateRemoveButtons() {
    const groups = document.querySelectorAll('.file-input-compact');
    groups.forEach((group, index) => {
        const removeBtn = group.querySelector('.remove-file');
        if (groups.length > 1) {
            removeBtn.style.display = 'block';
        } else {
            removeBtn.style.display = 'none';
        }
    });
}

// Compact upload zone click handler
document.addEventListener('DOMContentLoaded', function() {
    const uploadZone = document.querySelector('.upload-zone-compact');
    if (uploadZone) {
        uploadZone.addEventListener('click', function() {
            const firstFileInput = document.querySelector('input[type="file"]');
            if (firstFileInput) {
                firstFileInput.click();
            }
        });
    }
});

// Form validation
document.getElementById('evidenceForm').addEventListener('submit', function(e) {
    const fileInputs = document.querySelectorAll('input[type="file"]');
    let hasFiles = false;
    
    fileInputs.forEach(input => {
        if (input.files.length > 0) {
            hasFiles = true;
        }
    });
    
    if (!hasFiles) {
        e.preventDefault();
        alert('Please select at least one file to upload.');
        return false;
    }
    
    // Check file sizes
    let oversizedFiles = [];
    fileInputs.forEach((input, index) => {
        if (input.files.length > 0) {
            const file = input.files[0];
            if (file.size > 5 * 1024 * 1024) { // 5MB
                oversizedFiles.push(file.name);
            }
        }
    });
    
    if (oversizedFiles.length > 0) {
        e.preventDefault();
        alert('The following files are too large (max 5MB): ' + oversizedFiles.join(', '));
        return false;
    }
    
    // Confirm upload
    if (!confirm('Are you sure you want to upload these evidence files for {{ $station->name }}?')) {
        e.preventDefault();
        return false;
    }
});

// Initialize remove buttons
document.addEventListener('DOMContentLoaded', function() {
    updateRemoveButtons();
});
</script>
@endsection
