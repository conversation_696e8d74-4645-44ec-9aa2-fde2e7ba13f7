@extends('layouts.app')

@section('styles')
<style>
/* Compact Statistics Page Styles */
.stats-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.stats-card {
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.compact-metric {
    background: rgba(13, 110, 253, 0.1);
    border-left: 4px solid #0d6efd;
    padding: 0.75rem;
    border-radius: 0 4px 4px 0;
    margin-bottom: 0.75rem;
}

.metric-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.progress-compact {
    height: 6px;
    border-radius: 3px;
}

.date-filter {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 0.75rem;
    border: 1px solid #dee2e6;
}
</style>
@endsection

@section('content')
<div class="container-fluid">
    <!-- Compact Header Section -->
    <div class="stats-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h4 class="mb-1 fw-bold">
                    <i class="bi bi-graph-up text-primary me-2"></i>
                    Audit Statistics
                </h4>
                <p class="text-muted mb-0 small">Comprehensive analytics and reporting for vote submission audit data</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{{ route('admin.audit.activity') }}" class="btn btn-sm btn-outline-success">
                    <i class="bi bi-activity me-1"></i>
                    Live Activity
                </a>
                <a href="{{ route('admin.audit.index') }}" class="btn btn-sm btn-outline-secondary">
                    <i class="bi bi-arrow-left me-1"></i>
                    Back to Audit
                </a>
            </div>
        </div>
    </div>

    <!-- Compact Date Range Filter -->
    <div class="date-filter mb-3">
        <form method="GET" class="row g-2 align-items-end">
            <div class="col-md-3">
                <label for="start_date" class="form-label small fw-bold">Start Date</label>
                <input type="date" class="form-control form-control-sm" id="start_date" name="start_date"
                       value="{{ $startDate->format('Y-m-d') }}">
            </div>
            <div class="col-md-3">
                <label for="end_date" class="form-label small fw-bold">End Date</label>
                <input type="date" class="form-control form-control-sm" id="end_date" name="end_date"
                       value="{{ $endDate->format('Y-m-d') }}">
            </div>
            <div class="col-md-6">
                <div class="d-flex gap-2 align-items-center">
                    <button type="submit" class="btn btn-sm btn-primary">
                        <i class="bi bi-search me-1"></i>
                        Update Statistics
                    </button>
                    <div class="ms-auto">
                        <small class="text-muted">
                            <i class="bi bi-calendar-range me-1"></i>
                            {{ $startDate->format('M j') }} - {{ $endDate->format('M j, Y') }}
                            ({{ $startDate->diffInDays($endDate) + 1 }} days)
                        </small>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- Compact Statistics Overview -->
    <div class="row g-3 mb-3">
        <div class="col-lg-3 col-md-6">
            <div class="compact-metric">
                <div class="d-flex align-items-center">
                    <div class="metric-icon bg-primary bg-opacity-10 me-3">
                        <i class="bi bi-file-earmark-text text-primary"></i>
                    </div>
                    <div>
                        <h5 class="mb-0 fw-bold">{{ number_format($statistics['total_submissions']) }}</h5>
                        <small class="text-muted">Total Submissions</small>
                        <div class="small text-muted">
                            {{ round($statistics['total_submissions'] / max($startDate->diffInDays($endDate) + 1, 1), 1) }}/day avg
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="compact-metric" style="background: rgba(25, 135, 84, 0.1); border-left-color: #198754;">
                <div class="d-flex align-items-center">
                    <div class="metric-icon bg-success bg-opacity-10 me-3">
                        <i class="bi bi-people text-success"></i>
                    </div>
                    <div>
                        <h5 class="mb-0 fw-bold">{{ number_format($statistics['unique_agents']) }}</h5>
                        <small class="text-muted">Active Agents</small>
                        <div class="small text-muted">
                            {{ round($statistics['total_submissions'] / max($statistics['unique_agents'], 1), 1) }} submissions/agent
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="compact-metric" style="background: rgba(255, 193, 7, 0.1); border-left-color: #ffc107;">
                <div class="d-flex align-items-center">
                    <div class="metric-icon bg-warning bg-opacity-10 me-3">
                        <i class="bi bi-flag text-warning"></i>
                    </div>
                    <div>
                        <h5 class="mb-0 fw-bold">{{ number_format($statistics['flagged_submissions']) }}</h5>
                        <small class="text-muted">Flagged Submissions</small>
                        <div class="small text-muted">
                            {{ $statistics['total_submissions'] > 0 ? round(($statistics['flagged_submissions'] / $statistics['total_submissions']) * 100, 1) : 0 }}% of total
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="compact-metric" style="background: rgba(13, 202, 240, 0.1); border-left-color: #0dcaf0;">
                <div class="d-flex align-items-center">
                    <div class="metric-icon bg-info bg-opacity-10 me-3">
                        <i class="bi bi-check-circle text-info"></i>
                    </div>
                    <div>
                        <h5 class="mb-0 fw-bold">{{ number_format($statistics['verified_submissions']) }}</h5>
                        <small class="text-muted">Verified Submissions</small>
                        <div class="small text-muted">
                            {{ $statistics['total_submissions'] > 0 ? round(($statistics['verified_submissions'] / $statistics['total_submissions']) * 100, 1) : 0 }}% of total
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Compact Submission Methods and Metrics -->
    <div class="row g-3 mb-3">
        <div class="col-lg-6">
            <div class="card stats-card">
                <div class="card-header bg-light py-2">
                    <h6 class="mb-0 fw-bold">
                        <i class="bi bi-pie-chart me-2"></i>
                        Submission Methods
                    </h6>
                </div>
                <div class="card-body p-3">
                    <div class="row g-2">
                        <div class="col-6">
                            <div class="compact-metric" style="background: rgba(25, 135, 84, 0.1); border-left-color: #198754;">
                                <div class="d-flex align-items-center">
                                    <div class="metric-icon bg-success bg-opacity-10 me-2" style="width: 32px; height: 32px; font-size: 1rem;">
                                        <i class="bi bi-phone text-success"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-0 fw-bold">{{ number_format($statistics['api_submissions']) }}</h6>
                                        <small class="text-muted">Mobile App</small>
                                        <div class="small text-muted">
                                            {{ $statistics['total_submissions'] > 0 ? round(($statistics['api_submissions'] / $statistics['total_submissions']) * 100, 1) : 0 }}%
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="compact-metric" style="background: rgba(13, 202, 240, 0.1); border-left-color: #0dcaf0;">
                                <div class="d-flex align-items-center">
                                    <div class="metric-icon bg-info bg-opacity-10 me-2" style="width: 32px; height: 32px; font-size: 1rem;">
                                        <i class="bi bi-globe text-info"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-0 fw-bold">{{ number_format($statistics['web_submissions']) }}</h6>
                                        <small class="text-muted">Web Portal</small>
                                        <div class="small text-muted">
                                            {{ $statistics['total_submissions'] > 0 ? round(($statistics['web_submissions'] / $statistics['total_submissions']) * 100, 1) : 0 }}%
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @if($statistics['manager_submissions'] > 0)
                        <div class="col-12">
                            <div class="compact-metric" style="background: rgba(255, 193, 7, 0.1); border-left-color: #ffc107;">
                                <div class="d-flex align-items-center">
                                    <div class="metric-icon bg-warning bg-opacity-10 me-2" style="width: 32px; height: 32px; font-size: 1rem;">
                                        <i class="bi bi-person-gear text-warning"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-0 fw-bold">{{ number_format($statistics['manager_submissions']) }}</h6>
                                        <small class="text-muted">Manager Portal</small>
                                        <div class="small text-muted">
                                            {{ $statistics['total_submissions'] > 0 ? round(($statistics['manager_submissions'] / $statistics['total_submissions']) * 100, 1) : 0 }}%
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent">
                    <h5 class="mb-0">
                        <i class="bi bi-building me-2"></i>
                        Geographic Distribution
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center p-3">
                                <div class="bg-primary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 60px; height: 60px;">
                                    <i class="bi bi-geo-alt text-primary fs-4"></i>
                                </div>
                                <h4 class="mb-1">{{ number_format($statistics['unique_stations']) }}</h4>
                                <p class="text-muted mb-0">Polling Stations</p>
                                <small class="text-muted">Active locations</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center p-3">
                                <div class="bg-secondary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 60px; height: 60px;">
                                    <i class="bi bi-calculator text-secondary fs-4"></i>
                                </div>
                                <h4 class="mb-1">{{ number_format($statistics['average_votes_per_submission'] ?? 0) }}</h4>
                                <p class="text-muted mb-0">Avg Votes</p>
                                <small class="text-muted">Per submission</small>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <div class="text-center p-3 border-top">
                                <div class="bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 50px; height: 50px;">
                                    <i class="bi bi-arrow-up-right text-success fs-5"></i>
                                </div>
                                <h5 class="mb-1">{{ number_format($statistics['total_vote_changes'] ?? 0, 0, '.', ',') }}</h5>
                                <p class="text-muted mb-0">Total Vote Changes</p>
                                <small class="text-muted">Net vote difference</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Compact Quality Metrics -->
    <div class="card stats-card mb-3">
        <div class="card-header bg-light py-2">
            <h6 class="mb-0 fw-bold">
                <i class="bi bi-speedometer2 me-2"></i>
                Data Quality Metrics
            </h6>
        </div>
        <div class="card-body p-3">
            <div class="row g-3">
                <div class="col-md-4">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span class="small fw-semibold">Verification Rate</span>
                        <span class="badge bg-success">
                            {{ $statistics['total_submissions'] > 0 ? round(($statistics['verified_submissions'] / $statistics['total_submissions']) * 100, 1) : 0 }}%
                        </span>
                    </div>
                    <div class="progress progress-compact">
                        <div class="progress-bar bg-success"
                             style="width: {{ $statistics['total_submissions'] > 0 ? round(($statistics['verified_submissions'] / $statistics['total_submissions']) * 100, 1) : 0 }}%"></div>
                    </div>
                    <small class="text-muted">{{ number_format($statistics['verified_submissions']) }} of {{ number_format($statistics['total_submissions']) }} verified</small>
                </div>
                <div class="col-md-4">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span class="small fw-semibold">Flag Rate</span>
                        <span class="badge bg-warning">
                            {{ $statistics['total_submissions'] > 0 ? round(($statistics['flagged_submissions'] / $statistics['total_submissions']) * 100, 1) : 0 }}%
                        </span>
                    </div>
                    <div class="progress progress-compact">
                        <div class="progress-bar bg-warning"
                             style="width: {{ $statistics['total_submissions'] > 0 ? round(($statistics['flagged_submissions'] / $statistics['total_submissions']) * 100, 1) : 0 }}%"></div>
                    </div>
                    <small class="text-muted">{{ number_format($statistics['flagged_submissions']) }} submissions flagged</small>
                </div>
                <div class="col-md-4">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span class="small fw-semibold">Mobile App Usage</span>
                        <span class="badge bg-info">
                            {{ $statistics['total_submissions'] > 0 ? round(($statistics['api_submissions'] / $statistics['total_submissions']) * 100, 1) : 0 }}%
                        </span>
                    </div>
                    <div class="progress progress-compact">
                        <div class="progress-bar bg-info"
                             style="width: {{ $statistics['total_submissions'] > 0 ? round(($statistics['api_submissions'] / $statistics['total_submissions']) * 100, 1) : 0 }}%"></div>
                    </div>
                    <small class="text-muted">{{ number_format($statistics['api_submissions']) }} mobile submissions</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Compact Export and Actions -->
    <div class="card stats-card">
        <div class="card-header bg-light py-2">
            <h6 class="mb-0 fw-bold">
                <i class="bi bi-download me-2"></i>
                Export & Actions
            </h6>
        </div>
        <div class="card-body p-3">
            <div class="d-flex gap-2 flex-wrap">
                <a href="{{ route('admin.audit.export') }}?start_date={{ $startDate->format('Y-m-d') }}&end_date={{ $endDate->format('Y-m-d') }}"
                   class="btn btn-sm btn-outline-primary">
                    <i class="bi bi-file-earmark-excel me-1"></i>
                    Export to Excel
                </a>
                <a href="{{ route('admin.audit.flagged') }}" class="btn btn-sm btn-outline-warning">
                    <i class="bi bi-flag me-1"></i>
                    Review Flagged ({{ $statistics['flagged_submissions'] }})
                </a>
                <a href="{{ route('admin.audit.activity') }}" class="btn btn-sm btn-outline-success">
                    <i class="bi bi-activity me-1"></i>
                    Live Activity Monitor
                </a>
                <button class="btn btn-sm btn-outline-info" onclick="window.print()">
                    <i class="bi bi-printer me-1"></i>
                    Print Report
                </button>
            </div>
        </div>
    </div>
</div>

<style>
@media print {
    .btn, .card-header .btn {
        display: none !important;
    }
    
    .container-fluid {
        padding: 0 !important;
    }
    
    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }
}
</style>
@endsection
