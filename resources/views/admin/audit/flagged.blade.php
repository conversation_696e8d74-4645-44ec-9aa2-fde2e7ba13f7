@extends('layouts.app')

@section('styles')
<style>
/* Compact Audit Page Styles */
.audit-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.audit-card {
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.audit-table {
    font-size: 0.85rem;
}

.audit-table th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    font-size: 0.8rem;
    padding: 0.5rem;
}

.audit-table td {
    padding: 0.5rem;
    vertical-align: middle;
}

.flag-badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
}

.action-btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

.compact-stats {
    background: rgba(255, 193, 7, 0.1);
    border-left: 4px solid #ffc107;
    padding: 0.75rem;
    border-radius: 0 4px 4px 0;
}
</style>
@endsection

@section('content')
<div class="container-fluid">
    <!-- Compact Header Section -->
    <div class="audit-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h4 class="mb-1 fw-bold">
                    <i class="bi bi-flag text-warning me-2"></i>
                    Flagged Submissions
                </h4>
                <p class="text-muted mb-0 small">Review and manage submissions flagged for suspicious activity</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{{ route('admin.audit.activity') }}" class="btn btn-sm btn-outline-success">
                    <i class="bi bi-activity me-1"></i>
                    Live Activity
                </a>
                <a href="{{ route('admin.audit.index') }}" class="btn btn-sm btn-outline-secondary">
                    <i class="bi bi-arrow-left me-1"></i>
                    Back to Audit
                </a>
            </div>
        </div>
    </div>

    <!-- Compact Statistics -->
    <div class="row g-3 mb-3">
        <div class="col-lg-3 col-md-6">
            <div class="compact-stats">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <i class="bi bi-flag-fill text-warning" style="font-size: 1.5rem;"></i>
                    </div>
                    <div>
                        <h5 class="mb-0 fw-bold">{{ $suspiciousActivity['total_flagged'] }}</h5>
                        <small class="text-muted">Total Flagged</small>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="compact-stats" style="background: rgba(220, 53, 69, 0.1); border-left-color: #dc3545;">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <i class="bi bi-speedometer2 text-danger" style="font-size: 1.5rem;"></i>
                    </div>
                    <div>
                        <h5 class="mb-0 fw-bold">{{ $suspiciousActivity['rapid_submissions'] }}</h5>
                        <small class="text-muted">Rapid Submissions</small>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="compact-stats" style="background: rgba(13, 202, 240, 0.1); border-left-color: #0dcaf0;">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <i class="bi bi-arrow-up-right text-info" style="font-size: 1.5rem;"></i>
                    </div>
                    <div>
                        <h5 class="mb-0 fw-bold">{{ $suspiciousActivity['large_changes'] }}</h5>
                        <small class="text-muted">Large Changes</small>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="compact-stats" style="background: rgba(108, 117, 125, 0.1); border-left-color: #6c757d;">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <i class="bi bi-clock text-secondary" style="font-size: 1.5rem;"></i>
                    </div>
                    <div>
                        <h5 class="mb-0 fw-bold">{{ $suspiciousActivity['unusual_timing'] }}</h5>
                        <small class="text-muted">Unusual Timing</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Flagged Submissions Table -->
    <div class="card audit-card">
        <div class="card-header bg-light py-2">
            <h6 class="mb-0 fw-bold">
                <i class="bi bi-list-ul me-2"></i>
                Flagged Submissions Requiring Review
            </h6>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0 audit-table">
                    <thead>
                        <tr>
                            <th>Time</th>
                            <th>Agent</th>
                            <th>Station</th>
                            <th>Candidate</th>
                            <th>Vote Change</th>
                            <th>Flag Reason</th>
                            <th>Method</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($flaggedSubmissions as $log)
                        <tr class="table-warning">
                            <td>
                                <div class="fw-semibold">{{ $log->submission_time->format('M j, Y H:i') }}</div>
                                <small class="text-muted">{{ $log->submission_time->diffForHumans() }}</small>
                            </td>
                            <td>
                                <div class="fw-semibold">{{ $log->agent->user->name }}</div>
                                <small class="text-muted">{{ $log->agent->user->phone_number }}</small>
                            </td>
                            <td>{{ $log->pollingStation->name }}</td>
                            <td>
                                <div class="fw-semibold">{{ $log->candidate->name }}</div>
                                <small class="text-muted">{{ $log->candidate->position->name }}</small>
                            </td>
                            <td>
                                <span class="badge bg-primary">{{ number_format($log->new_votes) }}</span>
                                @if($log->vote_difference != 0)
                                    <div class="small text-{{ $log->vote_difference > 0 ? 'success' : 'danger' }}">
                                        {{ $log->vote_difference > 0 ? '+' : '' }}{{ number_format($log->vote_difference) }}
                                    </div>
                                @endif
                            </td>
                            <td>
                                <span class="badge bg-warning">{{ $log->flag_reason }}</span>
                                @if($log->flag_notes)
                                    <div class="small text-muted mt-1">{{ $log->flag_notes }}</div>
                                @endif
                            </td>
                            <td>
                                <span class="badge bg-{{ $log->submission_method === 'api' ? 'success' : 'info' }}">
                                    {{ ucfirst($log->submission_method) }}
                                </span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ route('admin.audit.agent.performance', $log->agent) }}" 
                                       class="btn btn-outline-primary btn-sm" 
                                       title="Agent Performance">
                                        <i class="bi bi-speedometer2"></i>
                                    </a>
                                    
                                    @if(!$log->is_verified)
                                        <button class="btn btn-outline-success btn-sm" 
                                                onclick="verifySubmission({{ $log->id }})" 
                                                title="Verify">
                                            <i class="bi bi-check"></i>
                                        </button>
                                    @endif
                                    
                                    <button class="btn btn-outline-info btn-sm" 
                                            onclick="unflagSubmission({{ $log->id }})" 
                                            title="Remove Flag">
                                        <i class="bi bi-flag-fill"></i>
                                    </button>
                                    
                                    <button class="btn btn-outline-secondary btn-sm" 
                                            onclick="viewDetails({{ $log->id }})" 
                                            title="View Details">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="8" class="text-center py-5">
                                <div class="text-muted">
                                    <i class="bi bi-check-circle display-4 text-success"></i>
                                    <h5 class="mt-3">No Flagged Submissions</h5>
                                    <p class="mb-0">All submissions are currently clean. Great job!</p>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
        
        @if($flaggedSubmissions->hasPages())
        <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
                <div class="text-muted small">
                    Showing {{ $flaggedSubmissions->firstItem() }} to {{ $flaggedSubmissions->lastItem() }} 
                    of {{ $flaggedSubmissions->total() }} flagged submissions
                </div>
                <div>
                    {{ $flaggedSubmissions->links() }}
                </div>
            </div>
        </div>
        @endif
    </div>
</div>

<script>
function verifySubmission(logId) {
    if (confirm('Are you sure you want to verify this submission? This will remove the flag.')) {
        fetch(`/admin/audit/verify/${logId}`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error verifying submission');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error verifying submission');
        });
    }
}

function unflagSubmission(logId) {
    const notes = prompt('Enter notes for removing this flag (optional):');
    if (notes !== null) { // User didn't cancel
        fetch(`/admin/audit/unflag/${logId}`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ notes: notes })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error removing flag');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error removing flag');
        });
    }
}

function viewDetails(logId) {
    // This could open a modal with detailed information
    console.log('View details for log ID:', logId);
    // For now, just redirect to the main audit page with a filter
    window.location.href = `/admin/audit?search=${logId}`;
}
</script>
@endsection
